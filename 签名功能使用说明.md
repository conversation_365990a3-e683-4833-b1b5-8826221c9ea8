# 郑重声明页签名功能使用说明

## 功能概述

本次更新为郑重声明页添加了以下功能：
1. **签名图片插入**：可以在承诺人后插入签名图片
2. **日期变量化**：将原来固定的"年月日"格式改为可配置的日期变量

## 使用方法

### 1. 设置声明日期

在 `src/common/initialization.tex` 文件中，找到以下部分：

```latex
%-> 郑重声明日期设置（用于论文郑重声明页）
% 格式：YYYY年MM月DD日
% 例如：2024年12月28日
\declaredate{2024年12月28日}
```

修改日期为您需要的日期即可。

### 2. 添加签名图片

#### 步骤1：准备签名图片
- 将您的签名图片放在 `figures/` 文件夹中
- 支持的格式：PNG、JPG、PDF、SVG等
- 建议图片高度不超过2cm，宽度适中

#### 步骤2：配置签名图片
在 `src/common/initialization.tex` 文件中，找到以下部分：

```latex
%-> 签名图片设置（用于论文郑重声明页）
% 请将签名图片放在figures文件夹中，然后取消下面一行的注释并修改文件名
% \signature{figures/signature.png}
```

取消注释并修改为您的签名图片文件名：

```latex
\signature{figures/your_signature.png}
```

## 示例

我们提供了一个示例签名图片 `figures/signature_example.svg`，您可以参考使用：

```latex
\signature{figures/signature_example.svg}
```

## 注意事项

1. 如果不设置签名图片，郑重声明页将显示原来的"承诺人（签名）："格式
2. 如果不设置声明日期，将使用原来的"年月日"格式
3. 签名图片会自动调整高度为2em，请确保图片比例合适
4. 建议使用SVG格式的签名图片，可以获得更好的显示效果

## 技术实现

- 新增了 `\declaredate{}` 命令用于设置声明日期
- 新增了 `\signature{}` 命令用于设置签名图片路径
- 修改了郑重声明页的布局，支持条件显示签名图片和日期变量