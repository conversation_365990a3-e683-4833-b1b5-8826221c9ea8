% !TEX program = xelatex
% !TEX options = -synctex=1 -interaction=nonstopmode -shell-escape -file-line-error "%DOC%"
% !BIB program = bibtex
%---------------------------------------------------------------------------%
%-                                                                         -%
%-                     浙江传媒学院毕业论文模板                           -%
%-                                                                         -%
%---------------------------------------------------------------------------%
%- Copyright (C) Hao XIE <<EMAIL>>
%- This is free software: you can redistribute it and/or modify it
%- under the terms of the GNU General Public License as published by
%- the Free Software Foundation, either version 3 of the License, or
%- (at your option) any later version.
%---------------------------------------------------------------------------%
%->> 文档类声明
%---------------------------------------------------------------------------%
% 本文档使用 cuzthesis 文档类，专为浙江传媒学院毕业论文设计
% 以下是可用的选项及其说明：
%
% 【使用说明】
% 1. 首次使用时，请先阅读本文件中的注释，了解各选项的功能
% 2. 根据需要修改文档类选项和宏包选项
% 3. 在 src/common/initialization.tex 中填写论文基本信息
% 4. 在 src/thesis/frontmatter/abstracts.tex 中编写中英文摘要
% 5. 在 src/thesis/mainmatter/ 目录下编写各章节内容
% 6. 使用 ./scripts/run.sh -xa cuzthesis.tex 命令编译论文
%
\documentclass[singlesided,cuzhdr]{styles/cuzthesis}%

%->> 文档类选项说明
%---------------------------------------------------------------------------%
% 页面布局选项（三选一）：
% - singlesided：单面打印模式，页边距对称
% - doublesided：双面打印模式，页边距交替变化
% - printcopy：印刷出版模式，为装订留出额外空间

% 页眉页脚选项：
% - cuzhdr：启用浙传风格的页眉页脚

% 版本信息选项：
% - draftversion：显示草稿版本信息

% 字体设置选项：
% - fontset=<fandol|founder|mac|ubuntu|windows|...>：指定字体集，替代自动检测
%   常用值：
%   - windows：使用 Windows 系统字体（宋体、黑体等）
%   - mac：使用 macOS 系统字体
%   - ubuntu：使用 Ubuntu 系统字体
%   - fandol：使用开源的 Fandol 字体（TeX Live 自带）

% 国际化选项：
% - scheme=plain：国际学生论文写作模式，使用英文界面



% 其他选项：
% - 支持 ctex book 类的标准选项：draft、paper size、font size 等
%---------------------------------------------------------------------------%
%->> 文档设置
%---------------------------------------------------------------------------%
% artratex 宏包提供了多种文档设置选项，用于控制参考文献、图表、数学等功能
% 常用配置组合：
% - 基础配置：\usepackage[super]{styles/artratex}
% - 标准配置：\usepackage[super,list,table]{styles/artratex}
% - 完整配置：\usepackage[super,list,table,tikz,math,color]{styles/artratex}
% - 作者年份引用：\usepackage[authoryear,list,table]{styles/artratex}
\usepackage[super,list,table,tikz]{styles/artratex}% 文档设置

%->> artratex 宏包选项说明
%---------------------------------------------------------------------------%
% 参考文献处理器选项（二选一）：
% - bibtex：使用 BibTeX 处理参考文献（默认）
% - biber：使用 Biber 处理参考文献（支持更多功能）

% 引用样式选项（四选一）：
% - numbers：数字引用样式
%   - 文内引用：Jones [1]
%   - 括号引用：[1]
% - super：上标数字引用样式（默认）
%   - 文内引用：Jones^[1]
%   - 括号引用：^[1]
% - authoryear：作者-年份引用样式
%   - 文内引用：Jones (1995)
%   - 括号引用：(Jones, 1995)
% - alpha：字母数字混合引用样式
%   - 文内引用：不可用
%   - 括号引用：[Jon95]

% 页面布局选项：
% - geometry：通过 geometry 宏包重新配置页面布局

% 环境支持选项：
% - lscape：提供横向页面布局环境
% - myhdr：通过 fancyhdr 宏包启用自定义页眉页脚
% - color：通过 xcolor 宏包提供颜色支持
% - background：启用页面背景
% - tikz：通过 tikz 宏包提供复杂图表支持
% - table：通过 ctable 宏包提供复杂表格支持
% - list：提供增强的列表环境，用于算法和代码
% - math：启用额外的数学宏包

% artracom 宏包提供了用户自定义命令，如数学符号、向量表示等
\usepackage{styles/artracom}% 用户自定义命令
%---------------------------------------------------------------------------%
%->> 文档内容
%---------------------------------------------------------------------------%
\begin{document}

%->> 初始化信息
%---------------------------------------------------------------------------%
% 导入初始化文件，包含论文的基本信息（标题、作者、学院等）
% 在 src/common/initialization.tex 中修改以下信息：
% - 论文标题（中英文）
% - 作者姓名、学号
% - 指导教师姓名、职称
% - 学院、专业、班级
% - 毕业年份
\input{src/common/initialization}

%->> 前置部分：封面、声明、摘要、目录
%---------------------------------------------------------------------------%
% 设置页面布局为普通格式（无页眉页脚）
\plainmatter

% 生成论文封面
% 封面内容根据 initialization.tex 中的信息自动生成
% 如需修改封面样式，请修改 cuzthesis.cls 文件中的 \maketitle 命令
\maketitle

% 生成作者声明页
% 如需修改声明内容，请修改 cuzthesis.cls 文件中的 \makedeclaration 命令
\makedeclaration

% 更改页面布局为无页脚格式（有页眉无页脚）
\nofootermatter

% 导入中英文摘要
% 在 src/thesis/frontmatter/abstracts.tex 中编写中英文摘要和关键词
% 摘要应简明扼要地概述论文的主要内容，包括研究目的、方法、结果和结论
% 关键词应能反映论文的主题和特点，一般为 3-8 个
\input{src/thesis/frontmatter/abstracts}

% 生成目录
% 目录自动根据各级标题生成，无需手动编辑
% 如需添加图表目录，可在此处添加 \listoffigures 和 \listoftables 命令
\tableofcontents
% \listoffigures  % 图目录（可选）
% \listoftables   % 表目录（可选）
% \listoflistings % 代码目录（可选）

%->> 主体部分：论文正文
%---------------------------------------------------------------------------%
% 更改页面布局为主体格式（有页眉页脚）
\mainmatter

% 导入论文主体内容（包含所有章节）
% mainbody.tex 文件通过 \input 命令引用各个章节文件
% 如需添加或删除章节，请修改 src/thesis/mainmatter/mainbody.tex 文件
% 各章节文件位于 src/thesis/mainmatter/ 目录下
\input{src/thesis/mainmatter/mainbody}

%->> 后置部分：参考文献、致谢、附录
%---------------------------------------------------------------------------%
% 添加参考文献到目录和书签
\intotoc{\bibname}

% 生成参考文献
% 参考文献数据库文件为 bibliography/references.bib
% 如需添加新的参考文献，请编辑该文件
% 如需调整参考文献的行距，可取消下面的注释
% \begingroup
% \linespread{1.2}\selectfont
\bibliography{bibliography/references}
% \endgroup

% 导入致谢部分
% 在 src/thesis/backmatter/acknowledgement.tex 中编写致谢内容
% 致谢应简明扼要，对支持和帮助过论文工作的人员和单位表示感谢
\input{src/thesis/backmatter/acknowledgement}

% 设置为附录模式（章节编号变为"附录A"等格式）
\appendix

% 导入附录内容（若无则可注释掉）
% 在 src/thesis/backmatter/appendices.tex 中编写附录内容
% 附录可包含对正文的补充说明、源代码、数据表格等内容
\input{src/thesis/backmatter/appendices}
\end{document}
%---------------------------------------------------------------------------%

