% \chapter{中国科学院大学学位论文撰写要求}
\begin{appendices}\label{sec:appendices}

    \chapter{附录示例章节}

    \section{测试公式编号} \label{sec:testmath}

    参见式\eqref{eq:appedns}与式\eqref{eq:2}：

    \begin{equation} \label{eq:appedns}
        \begin{cases}
            \frac{\partial \rho}{\partial t} + \nabla\cdot(\rho\Vector{V}) = 0 \ \mathrm{times\ font\ test}                                            \\
            \frac{\partial (\rho\Vector{V})}{\partial t} + \nabla\cdot(\rho\Vector{V}\Vector{V}) = \nabla\cdot\Tensor{\sigma} \ \text{times font test} \\
            \frac{\partial (\rho E)}{\partial t} + \nabla\cdot(\rho E\Vector{V}) = \nabla\cdot(k\nabla T) + \nabla\cdot(\Tensor{\sigma}\cdot\Vector{V})
        \end{cases}
    \end{equation}
    \begin{equation} \label{eq:2}
        \frac{\partial }{\partial t}\int\limits_{\Omega} u \, \mathrm{d}\Omega + \int\limits_{S} \unitVector{n}\cdot(u\Vector{V}) \, \mathrm{d}S = \dot{\phi}
    \end{equation}

    \section{测试代码段编号} \label{sec:testlistings}

    参见代码段
    \ref{code:samp-code-c}、\ref{code:samp-code-cpp}、\ref{code:samp-code-java}、\ref{code:samp-code-csharp}、
    \ref{code:samp-code-python}与\ref{code:samp-code-latex}：

    \begin{listing}[H]
        \centering
        \caption{一段简单得不能再简单的C代码}
        \label{code:samp-code-c}
        \begin{ccode}
            /* 一段简单得不能再简单的C代码 */
            #include <stdio.h>

            int main(int argc, char const **argv) {
                printf("Hello CUZThesis!!\n");
                return 0;
            }
        \end{ccode}
    \end{listing}

    \begin{listing}[H]
        \centering
        \caption{一段简单得不能再简单的C++代码}
        \label{code:samp-code-cpp}
        \begin{cppcode}
            /* 一段简单得不能再简单的C++代码 */
            #include <iostream>

            int main(int argc, char const **argv) {
                std::cout << "Hello CUZThesis!!" << std::endl;
                return 0;
            }
        \end{cppcode}
    \end{listing}

    \begin{listing}[H]
        \centering
        \caption{一段简单得不能再简单的Java代码}
        \label{code:samp-code-java}
        \begin{javacode}
            // 一段简单得不能再简单的Java代码
            class HelloCUZThesis {
                public static void main(String[] args) {
                    System.out.println("Hello CUZThesis!!");
                }
            }
        \end{javacode}
    \end{listing}

    \begin{listing}[H]
        \centering
        \caption{一段简单得不能再简单的C\#代码}
        \label{code:samp-code-csharp}
        \begin{csharpcode}
            // 一段简单得不能再简单的C#代码
            class HelloCUZThesis {
                public static void Main(string[] args) {
                    Console.WriteLine("Hello CUZThesis!!");
                }
            }
        \end{csharpcode}
    \end{listing}

    \begin{listing}[H]
        \centering
        \caption{一段简单得不能再简单的Python代码}
        \label{code:samp-code-python}
        \begin{pythoncode}
            # 一段简单得不能再简单的Python代码
            print("Hello CUZThesis!!")
        \end{pythoncode}
    \end{listing}

    \begin{listing}[H]
        \centering
        \caption{一段简单得不能再简单的\hologo{LaTeX}代码}
        \label{code:samp-code-latex}
        \begin{texcode}[texcomments]
            % 一段简单得不能再简单的\hologo{LaTeX}代码
            \documentclass{article}

            \begin{document}
                Hello CUZThesis!!
            \end{document}
        \end{texcode}
    \end{listing}

    \section{测试生僻字} \label{sec:testcharacters}

    霜蟾盥薇曜灵霜颸妙鬘虚霩淩澌菀枯菡萏泬寥窅冥毰毸濩落霅霅便嬛岧峣瀺灂姽婳愔嫕
    飒纚棽俪緸冤莩甲摛藻卮言倥侗椒觞期颐夜阑彬蔚倥偬澄廓簪缨陟遐迤逦缥缃鹣鲽憯懔
    闺闼璀错媕婀噌吰澒洞阛闠覼缕玓瓑逡巡諓諓琭琭瀌瀌踽踽叆叇氤氲瓠犀流眄蹀躞赟嬛
    茕頔璎珞螓首蘅皋惏悷缱绻昶皴皱颟顸愀然菡萏卑陬纯懿犇麤掱暒墌墍墎墏墐墒墒墓墔
    墕墖墘墖墚墛坠墝增墠墡墢墣墤墥墦墧墨墩墪樽墬墭堕墯墰墱墲坟墴墵垯墷墸墹墺墙墼
    墽垦墿壀壁壂壃壄壅壆坛壈壉壊垱壌壍埙壏壐壑壒压壔壕壖壗垒圹垆壛壜壝垄壠壡坜壣
    壤壥壦壧壨坝塆圭嫶嫷嫸嫹嫺娴嫼嫽嫾婳妫嬁嬂嬃嬄嬅嬆嬇娆嬉嬊娇嬍嬎嬏嬐嬑嬒嬓嬔
    嬕嬖嬗嬘嫱嬚嬛嬜嬞嬟嬠嫒嬢嬣嬥嬦嬧嬨嬩嫔嬫嬬奶嬬嬮嬯婴嬱嬲嬳嬴嬵嬶嬷婶嬹嬺嬻
    嬼嬽嬾嬿孀孁孂娘孄孅孆孇孆孈孉孊娈孋孊孍孎孏嫫婿媚嵭嵮嵯嵰嵱嵲嵳嵴嵵嵶嵷嵸嵹
    嵺嵻嵼嵽嵾嵿嶀嵝嶂嶃崭嶅嶆岖嶈嶉嶊嶋嶌嶍嶎嶏嶐嶑嶒嶓嵚嶕嶖嶘嶙嶚嶛嶜嶝嶞嶟峤
    嶡峣嶣嶤嶥嶦峄峃嶩嶪嶫嶬嶭崄嶯嶰嶱嶲嶳岙嶵嶶嶷嵘嶹岭嶻屿岳帋巀巁巂巃巄巅巆巇
    巈巉巊岿巌巍巎巏巐巑峦巓巅巕岩巗巘巙巚帠帡帢帣帤帨帩帪帬帯帰帱帲帴帵帷帹帺帻
    帼帽帾帿幁幂帏幄幅幆幇幈幉幊幋幌幍幎幏幐幑幒幓幖幙幚幛幜幝幞帜幠幡幢幤幥幦幧
    幨幩幪幭幮幯幰幱庍庎庑庖庘庛庝庠庡庢庣庤庥庨庩庪庬庮庯庰庱庲庳庴庵庹庺庻庼庽
    庿廀厕廃厩廅廆廇廋廌廍庼廏廐廑廒廔廕廖廗廘廙廛廜廞庑廤廥廦廧廨廭廮廯廰痈廲廵
    廸廹廻廼廽廿弁弅弆弇弉弖弙弚弜弝弞弡弢弣弤弨弩弪弫弬弭弮弰弲弪弴弶弸弻弼弽弿
    彖彗彘彚彛彜彝彞彟彴彵彶彷彸役彺彻彽彾佛徂徃徆徇徉后徍徎徏径徒従徔徕徖徙徚徛
    徜徝从徟徕御徢徣徤徥徦徧徨复循徫旁徭微徯徰徱徲徳徴徵徶德徸彻徺忁忂惔愔忇忈忉
    忔忕忖忚忛応忝忞忟忪挣挦挧挨挩挪挫挬挭挮挰掇授掉掊掋掍掎掐掑排掓掔掕挜掚挂掜
    掝掞掟掠采探掣掤掦措掫掬掭掮掯掰掱掲掳掴掵掶掸掹掺掻掼掽掾掿拣揁揂揃揅揄揆揇
    揈揉揊揋揌揍揎揑揓揔揕揖揗揘揙揤揥揦揧揨揫捂揰揱揲揳援揵揶揷揸揻揼揾揿搀搁搂
    搃搄搅搇搈搉搊搋搌搎搏搐搑搒摓摔摕摖摗摙摚摛掼摝摞摠摡斫斩斮斱斲斳斴斵斶斸旪
    旫旮旯晒晓晔晕晖晗晘晙晛晜晞晟晠晡晰晣晤晥晦晧晪晫晬晭晰晱晲晳晴晵晷晸晹晻晼
    晽晾晿暀暁暂暃暄暅暆暇晕晖暊暋暌暍暎暏暐暑暒暓暔暕暖暗旸暙暚暛暜暝暞暟暠暡暣
    暤暥暦暧暨暩暪暬暭暮暯暰昵暲暳暴暵暶暷暸暹暺暻暼暽暾暿曀曁曂曃晔曅曈曊曋曌曍
    曎曏曐曑曒曓曔曕曗曘曙曚曛曜曝曞曟旷曡曢曣曤曥曦曧昽曩曪曫晒曭曮曯椗椘椙椚椛
    検椝椞椟椠椡椢椣椤椥椦椧椨椩椪椫椬椭椮椯椰椱椲椳椴椵椶椷椸椹椺椻椼椽椾椿楀楁
    楂楃楅楆楇楈楉杨楋楌楍榴榵榶榷榸榹榺榻榼榽榾桤槀槁槂盘槄槅槆槇槈槉槊构槌枪槎
    槏槐槑槒杠槔槕槖槗滙滛滜滝滞滟滠滢滣滦滧滪滫沪滭滮滰滱渗滳滵滶滹滺浐滼滽漀漃
    漄漅漈漉溇漋漌漍漎漐漑澙熹漗漘漙沤漛漜漝漞漟漡漤漥漦漧漨漪渍漭漮漯漰漱漳漴溆
    漶漷漹漺漻漼漽漾浆潀颍潂潃潄潅潆潇潈潉潊潋潌潍潎潏潐潒潓洁潕潖潗潘沩潚潜潝潞
    潟潠潡潢潣润潥潦潧潨潩潪潫潬潭浔溃潱潲潳潴潵潶滗潸潹潺潻潼潽潾涠澁澄澃澅浇涝
    澈澉澊澋澌澍澎澏湃澐澑澒澓澔澕澖涧澘澙澚澛澜澝澞澟渑澢澣泽浍澯澰淀澲澳澴澵澶
    澷澸潇潆瀡瀢瀣瀤瀥潴泷濑瀩瀪瀫瀬瀭瀮瀯弥瀱潋瀳瀴瀵瀶瀷瀸瀹瀺瀻瀼瀽澜瀿灀灁瀺
    灂沣滠灅灆灇灈灉灊灋灌灍灎灏灐洒灒灓漓灖灗滩灙灚灛灜灏灞灟灠灡灢湾滦灥灦灧灨
    灪燝燞燠燡燢燣燤燥灿燧燨燩燪燫燮燯燰燱燲燳烩燵燵燸燹燺薰燽焘燿爀爁爂爃爄爅爇
    爈爉爊爋爌烁爎爏爑爒爓爔爕爖爗爘爙爚烂爜爝爞爟爠爡爢爣爤爥爦爧爨爩猽猾獀犸獂
    獆獇獈獉獊獋獌獍獏獐獑獒獓獔獕獖獗獘獙獚獛獜獝獞獟獠獡獢獣獤獥獦獧獩狯猃獬獭
    狝獯狞獱獳獴獶獹獽獾獿猡玁玂玃。

    \begin{leftbar}
        \noindent\textbf{建议：}附录为可选部分，一般用来存放不适合在正文中列出的
        大段内容，如较长的表格、代码段等。若需分小节列出，则应采用加星号版本的小
        节命令\verb|\section*{}|，以避免出现不正确的章节号。
    \end{leftbar}

\end{appendices}