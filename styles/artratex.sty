%---------------------------------------------------------------------------%
%-                                                                         -%
%-                           Document Style                                -%
%-                                                                         -%
%---------------------------------------------------------------------------%
%- Copyright (C) Hao XIE <<EMAIL>>
%- This is free software: you can redistribute it and/or modify it
%- under the terms of the GNU General Public License as published by
%- the Free Software Foundation, either version 3 of the License, or
%- (at your option) any later version.
%---------------------------------------------------------------------------%
%->> Identification
%---------------------------------------------------------------------------%
\NeedsTeXFormat{LaTeX2e}%
\ProvidesPackage{artratex}[2014/10/01 v0.1 LaTeX macros package]%
%---------------------------------------------------------------------------%
%->> Declare options
%---------------------------------------------------------------------------%
%-
%-> Platform fontset <windows>, <mac>, <adobe>, <times>, <others>
%-
% \RequirePackage{expl3}% LaTeX3 programming environment
% \ExplSyntaxOn%
% \providecommand{\g__ctex_fontset_tl}{}% platform fontset state variable
% \edef\artxfontset{\g__ctex_fontset_tl}% expanded platform fontset state variable
% \ExplSyntaxOff%
% \newif\ifartx@windows \artx@windowsfalse
% \newif\ifartx@mac \artx@macfalse
% \newif\ifartx@adobe \artx@adobefalse
% \newif\ifartx@times \artx@timesfalse
% \newif\ifartx@others \artx@othersfalse
% \RequirePackage{etoolbox}% a toolbox of programming facilities
% \newcommand{\artxifstreq}{\expandafter\ifstrequal\expandafter}% expansion control
% \artxifstreq{\artxfontset}{windows}{\artx@windowstrue\artx@timestrue}{%
% \artxifstreq{\artxfontset}{mac}{\artx@mactrue\artx@timestrue}{%
% \artxifstreq{\artxfontset}{adobe}{\artx@adobetrue\artx@timestrue}{%
% \artx@otherstrue\artx@timesfalse}}}
\RequirePackage{ifplatform}% check which platform it is running on
%-
%-> LaTeX engine <pdflatex>, <lualatex>, <xelatex>
%-
% \newif\ifartx@pdftex \artx@pdftexfalse
% \newif\ifartx@luatex \artx@luatexfalse
% \newif\ifartx@xetex \artx@xetexfalse
\RequirePackage{iftex}% LaTeX engine detection
% \ifxetex%
%     \artx@xetextrue
%     % \RequirePackage{xeCJK}% support calling system fonts
% \else\ifluatex%
%     \artx@luatextrue
% \else%
%     \artx@pdftextrue
% \fi\fi%
%-
%-> Bibliography engine <bibtex>, <biber>
%-
\newif\ifartx@bibtex \artx@bibtextrue
\newif\ifartx@biber \artx@biberfalse
\DeclareOption{bibtex}{%
	\artx@bibtextrue
	\artx@biberfalse
}
\DeclareOption{biber}{%
	\artx@bibtexfalse
	\artx@bibertrue
}
%-
%-> Citation and reference style
%-
\newif\ifartx@numbers \artx@numberstrue
\newif\ifartx@super \artx@superfalse
\newif\ifartx@authoryear \artx@authoryearfalse
\newif\ifartx@alpha \artx@alphafalse
\DeclareOption{numbers}{%
	\artx@numberstrue
	\artx@superfalse
	\artx@authoryearfalse
	\artx@alphafalse
}
\DeclareOption{super}{%
	\artx@numberstrue
	\artx@supertrue
	\artx@authoryearfalse
	\artx@alphafalse
}
\DeclareOption{authoryear}{%
	\artx@numbersfalse
	\artx@superfalse
	\artx@authoryeartrue
	\artx@alphafalse
}
\DeclareOption{alpha}{%
	\artx@numbersfalse
	\artx@superfalse
	\artx@authoryearfalse
	\artx@alphatrue
}
%-
%-> Page layout reconfiguration
%-
\newif\ifartx@geometry \artx@geometryfalse
\DeclareOption{geometry}{%
	\artx@geometrytrue
}
%-
%-> Landscape layout support
%-
\newif\ifartx@lscape \artx@lscapefalse
\DeclareOption{lscape}{%
	\artx@lscapetrue
}
%-
%-> Header and footer
%-
\newif\ifartx@myhdr \artx@myhdrfalse
\DeclareOption{myhdr}{%
	\artx@myhdrtrue
}
%-
%-> Color support
%-
\newif\ifartx@color \artx@colorfalse
\DeclareOption{color}{%
	\artx@colortrue
}
%-
%-> Page background
%-
\newif\ifartx@background \artx@backgroundfalse
\DeclareOption{background}{%
	\artx@colortrue
	\artx@backgroundtrue
}
%-
%-> Complex diagrams support
%-
\newif\ifartx@tikz \artx@tikzfalse
\DeclareOption{tikz}{%
	\artx@colortrue
	\artx@tikztrue
}
%-
%-> Complex tables support
%-
\newif\ifartx@table \artx@tablefalse
\DeclareOption{table}{%
	\artx@tabletrue
}
%-
%-> Enhanced list
%-
\newif\ifartx@list \artx@listfalse
\DeclareOption{list}{%
	\artx@colortrue
	\artx@listtrue
}
%-
%-> Extra math support
%-
\newif\ifartx@math \artx@mathfalse
\DeclareOption{math}{%
	\artx@mathtrue
}
%-
%-> Handle non-implemented options
%-
\DeclareOption*{%
	\PackageWarning{artratex}{Unknown option '\CurrentOption'}%
}
%-
%-> Terminates all options processing
%-
\ProcessOptions\relax%
%---------------------------------------------------------------------------%
%->> Required packages
%---------------------------------------------------------------------------%
%-
%-> Math packages
%-
\RequirePackage{amsmath}% math packages
\ifartx@math% extra math packages
	\RequirePackage{mathtools,amsfonts}%
\fi
%-
%-> Language settings
%-
\ifpdftex% <pdflatex> call font packages
	\RequirePackage[utf8]{inputenc}% set input encoding, document must use utf-8 encoding
	\RequirePackage[T1]{fontenc}% set font encoding to enable modern font encoding
	%- Call font package options:
	%- Text + Math: Times Roman
	\RequirePackage{newtxtext}%
	\RequirePackage[cmintegrals]{newtxmath}% load after math packages
	%- Text + Math: Palatino
	%\RequirePackage{palatino}%
\else% <xelatex> or <lualatex> call system fonts
	% \ifxetex
	%     \RequirePackage{xeCJK}% support calling system fonts
	% \fi
	% \RequirePackage{fontspec}% support calling system fonts
	% \@ifpackagelater{fontspec}{2016/12/27}{}{%
	%     \PackageError{artratex}{fontspec >= 2016/12/27 is required ...}{}%
	% }
	% \ifmacosx
	% \else
	%     \RequirePackage{newtxtext}%
	% \fi
	\RequirePackage[cmintegrals]{newtxmath}% math symbol font, load after math but before fontspec packages
	\RequirePackage{mathrsfs}% enable \mathscr for script alphabet
	\RequirePackage[cal=cm]{mathalfa}%
	%- Font properties: <family> + <weight> + <shape> + <size>
	%- Specify the three default TeX font families: <main>, <sans>, <mono>
	%- \setxxxxfont{<font>}[BoldFont=<font-b>,ItalicFont=<font-i>]%
	%- <font>: \mdseries\upshape; <font-b>: \bfseries\upshape; <font-i>: \mdseries\itshape
	%- <\lfseries|\mdseries|\bfseries>: weight of font, default <\mdseries>
	%- <\upshape|\itshape|\scshape>: shape of font, default <\upshape>
	%- Roman or Serif - typefaces with strokes - for main content
	%- examples: Times New Roman, Garamond, Adobe Garamond Pro
	%\setmainfont{Garamond}[BoldFont=Garamond-Bold,ItalicFont=Garamond-Italic]%
	%- font switches: {\rmfamily ...}, \textrm{...}, {\normalfont ...}, \textnormal{...}
	%- the latter is due to \renewcommand*{\familydefault}{\rmdefault}
	%- Sans serif - typefaces without strokes - for headings demanding high readability
	%- examples: Arial, Helvetica, Gill Sans, Futura
	%\setsansfont{Gill Sans MT}% font switches: {\sffamily ...}, \textsf{...}
	%- Monospaced - typefaces with same width - for programming, etc
	%- examples: Rockwell, Andale Mono, Courier
	%\setmonofont{Rockwell}% font switches: {\ttfamily, ...}, \texttt{...}
	%- Specify user-defined font families and font switches
	%\newfontfamily\<font-switch>{<font>}[<font features>]%
	%\newcommand{\text<font-switch>}[1]{{\<font-switch> #1}}%
	% without NFSSFamily option, a font family is defined only when the added font name
	% is a new one. mathrm and mathsf can be set by \setmathrm and \setmathsf
	%\RequirePackage{unicode-math}% another math symbol font configuration
	%\setmathfont{XITS Math}% a complete symbol set for STIX math fonts
	\newcommand*{\weibei}{\CJKfamily{zhwei}}% add definition of WeiBei.
	\setmainfont{Times New Roman}% Times New Roman
	\ifwindows
		\renewcommand*{\lishu}{\CJKfamily{zhli}}% modify definition of Lishu on Windows.
		\renewcommand*{\youyuan}{\CJKfamily{zhyou}}% modify definition of YouYuan on Windows.
		\renewcommand*{\heiti}{\CJKfamily{zhhei}}% modify definition of Heiti on Windows.
		\setCJKfamilyfont{zhli}{隶书}[%
			AutoFakeBold=4,
		]%
		\setCJKfamilyfont{zhwei}{华文新魏}[%
			AutoFakeBold=4,
		]%
		\setCJKfamilyfont{zhyou}{幼圆}[%
			AutoFakeBold=4,
		]%
		\setCJKfamilyfont{zhhei}{黑体}[%
			AutoFakeBold=4,
		]%
		\setCJKmainfont{SimSun}[AutoFakeBold=4,ItalicFont=KaiTi]%
		\setCJKsansfont{SimHei}[AutoFakeBold=4]%
		\setCJKmonofont{FangSong}%
		\setmonofont{FiraMono Nerd Font}[
			Scale=MatchLowercase,
		]% Fira Mono for all platforms
	\else
		\setmonofont{Fira Mono}[
			Scale=MatchLowercase,
		]% Fira Mono for all platforms
		\newcommand*{\lishu}{\CJKfamily{zhli}}% add definition of Lishu on macOS and Linux.
		\newcommand*{\youyuan}{\CJKfamily{zhyou}}% add definition of YouYuan on macOS and Linux.
		\ifmacosx
			\setCJKfamilyfont{zhli}{Baoli SC}[%
				AutoFakeBold=4,
			]%
			\setCJKfamilyfont{zhwei}{Weibei SC}[%
				AutoFakeBold=4,
			]%
			\setCJKfamilyfont{zhyou}{Yuanti SC}[%
				AutoFakeBold=4,
			]%
			\setCJKmainfont{Songti SC}[%
				UprightFont    = * Light,
				BoldFont       = * Bold,
				ItalicFont     = Kaiti SC,
				BoldItalicFont = Kaiti SC Bold,
			]%
			\setCJKsansfont{Heiti SC}[%
				BoldFont = * Medium,
			]%
			\setCJKmonofont{STFangsong}[%
				AutoFakeBold=3,
			]%
			\setsansfont{Helvetica}% Helvetica
		\fi
		\iflinux
			\setCJKfamilyfont{zhli}{隶书}[%
				% \setCJKfamilyfont{zhli}{方正隶书_GBK}[%
				AutoFakeBold=4,
			]%
			\setCJKfamilyfont{zhwei}{华文新魏}[%
				% \setCJKfamilyfont{zhwei}{方正魏碑_GBK}[%
				AutoFakeBold=4,
			]%
			\setCJKfamilyfont{zhyou}{幼圆}[%
				AutoFakeBold=4,
			]%
			\setCJKfamilyfont{adobesongti}{AdobeSongStd-Light}[%
				AutoFakeBold=4,
				ItalicFont=AdobeKaitiStd-Regular,
			]%
			\renewcommand*{\songti}{\CJKfamily{adobesongti}}%
			\setCJKfamilyfont{adobeheiti}{AdobeHeitiStd-Regular}[%
				AutoFakeBold=4,
			]%
			\renewcommand*{\heiti}{\CJKfamily{adobeheiti}}%
			\setCJKfamilyfont{adobekaiti}{AdobeKaitiStd-Regular}[%
				AutoFakeBold=4,
			]%
			\newcommand*{\kaiti}{\CJKfamily{adobekaiti}}%
			\setCJKmainfont{AdobeSongStd-Light}[%
				AutoFakeBold=4,
				ItalicFont=AdobeKaitiStd-Regular,
			]
			\setCJKsansfont{AdobeHeitiStd-Regular}[%
				AutoFakeBold=4,
			]%
			\setCJKmonofont{AdobeFangsongStd-Regular}[%
				AutoFakeBold=4,
			]%
		\fi
	\fi
\fi
%-
%-> Bibliography processor and package
%-
%- Bibtex processor + natbib package
\ifartx@bibtex%
	\ifartx@numbers% enable numbered citation style
		\ifartx@super% enable superscripted citation style
			\RequirePackage[square,comma,super,sort&compress]{natbib}% superscripted square bracket
		\else
			\RequirePackage[square,comma,numbers,sort&compress]{natbib}% square bracket
		\fi
		\bibliographystyle{bibliography/gbt7714-unsrt}% numbered scheme
	\fi
	\ifartx@authoryear% enable author year citation style
		\RequirePackage{natbib}% author year citation mode
		\bibliographystyle{bibliography/gbt7714-plain}% author year scheme
	\fi
	\ifartx@alpha% enable alpha citation style
		\RequirePackage[square,comma,numbers]{natbib}% square bracket
		\bibliographystyle{alpha}% alpha scheme
	\fi
	\providecommand*{\citetns}[2][]{% text embedded \citet in superscripted mode
	\begingroup%
	\let\NAT@mbox=\mbox%
	\let\@cite\NAT@citenum%
	\let\NAT@space\NAT@spacechar%
	\let\NAT@super@kern\relax%
	\renewcommand\NAT@open{[}%
	\renewcommand\NAT@close{]}%
	\citet[#1]{#2}%
	\endgroup%
	}
	\providecommand*{\citepns}[2][]{% text embedded \citep in superscripted mode
	\begingroup%
	\let\NAT@mbox=\mbox%
	\let\@cite\NAT@citenum%
	\let\NAT@space\NAT@spacechar%
	\let\NAT@super@kern\relax%
	\renewcommand\NAT@open{[}%
	\renewcommand\NAT@close{]}%
	\citep[#1]{#2}%
	\endgroup%
	}
\fi
%- Biber processor + biblatex package
\ifartx@biber%
	\ifartx@numbers% enable numbered citation style
		\ifartx@super% enable superscripted citation style
			\RequirePackage[style=numeric-comp]{biblatex}%
		\else
			\RequirePackage[style=numeric-comp]{biblatex}%
		\fi
	\fi
	\ifartx@authoryear% enable author year citation style
		\RequirePackage[style=authoryear]{biblatex}%
	\fi
	\ifartx@alpha% enable alpha citation style
		\RequirePackage[style=alphabetic]{biblatex}%
	\fi
	\addbibresource{ref.bib}%
\fi
%-
%-> Figure environment support
%-
\RequirePackage{graphicx}% packages for including graphics
\RequirePackage[font={small,bf},skip=8pt,labelsep=space,font={bf,normalsize}]{caption}% options: [margin=10pt,labelfont=bf]
\RequirePackage{subcaption}% package for subfigures
\RequirePackage[list=off]{bicaption}% package for binary captions
\captionsetup[figure][bi-first]{format=hang,hangindent=-0.5em}%
\captionsetup[figure][bi-second]{format=hang,hangindent=-2em,name=Figure}%
\captionsetup[table][bi-first]{format=hang,hangindent=-0.5em}%
\captionsetup[table][bi-second]{format=hang,hangindent=-2em,name=Table}%
\RequirePackage[section]{placeins}% prevent floats from being moved over section
%-
%-> Page layout and spacing
%-
\ifartx@geometry% enable geometry to redefine page layout
	\RequirePackage{geometry}% page layout
	%\RequirePackage{setspace}% line spacing
\fi
\ifartx@lscape% landscape layout
	\RequirePackage{fancyhdr}% fancy headers and footers
	%- usage: \begin{landscape} [\thispagestyle{lscape}] text... \end{landscape}
	\RequirePackage{pdflscape}% landscape environment
	\RequirePackage[absolute]{textpos}% rotated page number
\fi
\ifartx@myhdr% header and footer style
	\RequirePackage{fancyhdr}% fancy headers and footers
\fi
%\RequirePackage{microtype}% improves general appearance of the text
%-
%-> Color
%-
\ifartx@color% enable color package to use color
	%\RequirePackage{color}%
	\RequirePackage[usenames,svgnames,table]{xcolor}%
\fi
%-
%-> Draw graphics directly with TeX commands
%-
\ifartx@tikz%
	\RequirePackage{tikz}% automatically load pgf package
	\usetikzlibrary{% load libraries
		positioning,
		arrows,
		calc,
		trees
	}%
\fi
%-
%-> Complex tables
%-
\ifartx@table%
	\RequirePackage{ctable}% imports the array, tabularx and booktabs packages
	\RequirePackage{footnote}% enable the footnotes in tables and tabulars
	\makesavenoteenv{table}
	\makesavenoteenv{tabular}
\fi
%-
%-> List structures
%-
\ifartx@list% enable enhanced list and verbatim structures
	\RequirePackage{verbatim}% improve verbatim environment
	\RequirePackage{enumitem}% configure the enumerate environment
	\setlist[enumerate]{wide=\parindent}% only indent the first line
	\setlist[itemize]{wide=\parindent}% only indent the first line
	\setlist{nosep}% default text spacing
	\RequirePackage{listings}% source code
	\RequirePackage{lstautogobble}% auto gobble for lstlisting
	\RequirePackage{algpseudocode,algorithm,algorithmicx}% algorithm
	\RequirePackage{relsize}
	\RequirePackage[newfloat=true]{minted}% better source code
\fi
%-
%-> Links support
%-
\RequirePackage{hyperref}%
\hypersetup{% set hyperlinks
	%bookmarks=true,% show bookmarks bar
	pdfencoding=auto,% allows non-Latin based languages in bookmarks
	%pdftitle={},% title
	%pdfauthor={},% author
	%pdfsubject={},% subject
	%pdftoolbar=true,% show toolbar
	%pdfmenubar=true,% show menu
	pdffitwindow=false,% window fit to page when opened
	pdfstartview={FitH},% fits the width of the page to the window
	%pdfnewwindow=true,% links in new window
	%backref=true,% do bibliographical back references
	%pagebackref=true,% backreference by page number
	colorlinks=true,% false: boxed links; true: colored links
	linkcolor=black,% color of internal links
	citecolor=blue,% color of links to bibliography
	filecolor=magenta,% color of file links
	urlcolor=brown,% color of external links
	bookmarksnumbered=true,% put section numbers in bookmarks
	%hidelinks% remove link color and border
}
%---------------------------------------------------------------------------%
%->> Configuration command
%---------------------------------------------------------------------------%
%-
%-> Extensions and directories for graphics
%-
%- Declare graphic extensions for automatic selection when including graphics
%- via avoiding supplying graphic extensions in \includegraphics command,
%- the source file can be more general and adaptive
\ifxetex%
	\DeclareGraphicsExtensions{.pdf,.png,.jpg,.eps,.tif,.bmp,.gif}%
\else% <pdflatex> or <lualatex>
	\DeclareGraphicsExtensions{.pdf,.png,.jpg}%
\fi
\graphicspath{{figures/}}% search path for figures
%-
%-> Layout, space, and style
%-
\ifartx@geometry% enable geometry to redefine page layout
	\geometry{paper=a4paper,left=31.7mm,right=31.7mm,top=25.4mm,bottom=25.4mm}%
\fi
%\linespread{1.5}% 1.5 for "one and a half" line spacing, and 2.0 for "double" line spacing
%\setlength{\parskip}{0.5ex plus 0.25ex minus 0.25ex}% skip space a paragraph
\setcounter{tocdepth}{2}% depth for the table of contents
\setcounter{secnumdepth}{3}% depth for section numbering, default is 2(subsub)
%- Set equation, figure, table numbering
%\numberwithin{equation}{section}% set enumeration level
%\renewcommand{\theequation}{\thesection\arabic{equation}}% configure the label style
%\numberwithin{figure}{section}% set enumeration level
%\renewcommand{\thefigure}{\thesection\arabic{figure}}% configure the label style
%\numberwithin{table}{section}% set enumeration level
%\renewcommand{\thetable}{\thesection\arabic{table}}% configure the label style
%- Set bibliography entry
\ifartx@bibtex%
	% \setlength{\bibsep}{0.0ex plus 0.2ex minus 0.2ex}% set distance between entries
	\setlength{\bibsep}{0.1\baselineskip}
	\renewcommand{\bibpreamble}{\vspace*{1.5ex}}
\fi
\ifartx@biber%
	\setlength\bibitemsep{0.1\baselineskip}% set distance between entries
\fi
\renewcommand*{\bibfont}{\small}% set font size for bibliography
%-
%-> Nomenclature item
%-
\providecommand{\nomenclatureitem}[3][ ]{%
	\noindent\makebox[0.15\textwidth][l]{#2}{{#3}\hfill{#1}}\par
}
%-
%-> Macro for adding content link to the table of content and bookmark
%-
\providecommand{\intotoc}[2][chapter]{%
	\cleardoublepage% ensure correct page reference
	\markboth{\MakeUppercase{#2}}{}% set the leftmark
	\phantomsection% create link in bookmarks
	\addcontentsline{toc}{#1}{#2}% add content #2 to toc as #1
}
%-
%-> Page header and footer Style
%-
%- Page styles in Latex refers to headers and footers of a document.
%- These headers/footers typically contain document titles, chapter
%- or section numbers/names, and page numbers.
%- Configure fancy style
\ifartx@myhdr% user defined header and footer style
	\pagestyle{fancy}%
	\providecommand{\chaptermark}{}% compatibility for non-book classes
	\providecommand{\thechapter}{}% compatibility for non-book classes
	\providecommand{\CTEXthechapter}{\thechapter.}% compatibility for non ctex classes
	%- reset style of chapter and section mark to actual name
	\renewcommand{\chaptermark}[1]{\markboth{\MakeUppercase{#1}}{}}%
	\renewcommand{\sectionmark}[1]{\markright{\MakeUppercase{#1}}{}}%
	%- deactivate uppercase effect
	\renewcommand{\MakeUppercase}[1]{#1}%
	%- Define different kinds of header and footer for different parts
	\fancypagestyle{frontmatterstyle}{% style for frontmatter
		\fancyhf{}% clear fields
		\fancyhead[CE]{\footnotesize \@title}% structure elements
		\fancyhead[CO]{\footnotesize \leftmark}% structure elements
		\fancyfoot[CE]{\footnotesize \thepage}% page number
		\fancyfoot[CO]{\footnotesize \thepage}% page number
		\renewcommand{\headrulewidth}{0.8pt}% header rule
		\renewcommand{\footrulewidth}{0pt}% footer rule
	}
	\fancypagestyle{mainmatterstyle}{% style for mainmatter
		\fancyhf{}% clear fields
		\fancyhead[CE]{\footnotesize \@title}% structure elements
		\fancyhead[CO]{\footnotesize \CTEXthechapter\ \leftmark}% structure elements
		\fancyfoot[LE]{\footnotesize \thepage}% page number
		\fancyfoot[RO]{\footnotesize \thepage}% page number
		\renewcommand{\headrulewidth}{0.8pt}% header rule
		\renewcommand{\footrulewidth}{0pt}% footer rule
	}
	\fancypagestyle{backmatterstyle}{% header and footer style for backmatter
		\fancyhf{}% clear fields
		\fancyhead[CE]{\footnotesize \@title}% structure elements
		\fancyhead[CO]{\footnotesize \leftmark}% structure elements
		\fancyfoot[LE]{\footnotesize \thepage}% page number
		\fancyfoot[RO]{\footnotesize \thepage}% page number
		\renewcommand{\headrulewidth}{0.8pt}% header rule
		\renewcommand{\footrulewidth}{0pt}% footer rule
	}
	%- Redefine \frontmatter to include the change
	\providecommand{\frontmatter}{}% compatibility for non-book classes
	\let\myfrontmatter\frontmatter%
	\renewcommand{\frontmatter}{%
		\myfrontmatter%
		\pagestyle{frontmatterstyle}%
	}
	%- Redefine \mainmatter to include the change
	\providecommand{\mainmatter}{}% compatibility for non-book classes
	\let\mymainmatter\mainmatter%
	\renewcommand{\mainmatter}{%
		\mymainmatter%
		\pagestyle{mainmatterstyle}%
	}
	%- Redefine \backmatter to include the change
	\providecommand{\backmatter}{}% compatibility for non-book classes
	\let\mybackmatter\backmatter%
	\renewcommand{\backmatter}{%
		\mybackmatter%
		\pagestyle{backmatterstyle}%
	}
	%- Some Latex commands, like \chapter, use the \thispagestyle command
	%- to automatically switch to the plain page style, thus ignoring the
	%- page style currently in effect. To customize such pages you must
	%- redefine the plain pagestyle. If you want the plain style inherits
	%- the current style, comment all the lines in plain style definition.
	\fancypagestyle{plain}{%
		%\fancyhf{}% clear fields
		%\renewcommand{\headrulewidth}{0pt}% header rule
		%\renewcommand{\footrulewidth}{0pt}% footer rule
	}
	\fancypagestyle{noheaderstyle}{% header and footer style for no header
		\fancyhf{}% clear fields
		%\fancyhead[CE]{\footnotesize \@title}% structure elements
		%\fancyhead[CO]{\footnotesize \leftmark}% structure elements
		\fancyfoot[LE]{\footnotesize \thepage}% page number
		\fancyfoot[RO]{\footnotesize \thepage}% page number
		\renewcommand{\headrulewidth}{0pt}% header rule
		\renewcommand{\footrulewidth}{0pt}% footer rule
	}
\fi
%-
%-> Configure landscape environment
%-
\ifartx@lscape%
	\fancypagestyle{lscape}{% landscape layout style
		\fancyhf{}% clear fields
		\fancyfoot[CE,CO]{%
			%- textpos: \begin{textblock}{<blockwidth>}[0.5,0.5](<hpos>,<vpos>) text... \end{textblock}
			%- origin of the absolute coordinate is the top-left corner of the page
			%- [0.5,0.5] means reference point of the block is the middle
			\begin{textblock}{0.1}[0.5,0.5](0.85,0.5){\rotatebox{90}{\footnotesize \thepage}}\end{textblock}% position the page number
		}
		%- set units of <blockwidth>, <hpos>, and <vpos> arguments by corresponding modules
		\setlength{\TPHorizModule}{8.5in}% set to the width of page
		\setlength{\TPVertModule}{11in}% set to the height of page
		\renewcommand{\headrulewidth}{0pt}% header rule
		\renewcommand{\footrulewidth}{0pt}% footer rule
	}
\fi
\ifartx@list% enable enhanced list
	\definecolor{mygreen}{rgb}{0,0.6,0}
	\definecolor{mygray}{rgb}{0.5,0.5,0.5}
	\definecolor{mymauve}{rgb}{0.58,0,0.82}
	\lstset{%
		numberbychapter=true,% numbered sequentially or by chapter
		backgroundcolor=\color{white},% background color;
		basicstyle=\ttfamily\footnotesize,% font size for code
		breakatwhitespace=false,% sets if automatic breaks should only happen at whitespace
		breaklines=true,% sets automatic line breaking
		% captionpos=b,% caption-position to bottom
		commentstyle=\color{mygreen},% comment style
		% deletekeywords={...},% delete keywords from the given language
		frame=lines,% adds a frame around the code
		keepspaces=true,% keeps spaces in text for keeping indentation of code
		keywordstyle=\color{blue},% keyword style
		%otherkeywords={*,...},% add more keywords to the set
		numbers=left,% where to put the line-numbers; possible values are (none, left, right)
		numbersep=1.5em,% how far the line-numbers are from the code
		numberstyle=\tiny\color{mygray},% the style that is used for the line-numbers
		rulecolor=\color{black},% if not set, the frame-color may be changed on line-breaks
		showspaces=false,% show spaces everywhere adding particular underscores;
		showstringspaces=false,% underline spaces within strings only
		showtabs=false,% show tabs within strings adding particular underscores
		stepnumber=1,% the step between two line-numbers. If it's 1, each line will be numbered
		stringstyle=\color{mymauve},% string literal style
		tabsize=2,% sets default tabsize to 2 spaces
		title=\lstname,% show the filename of files
		autogobble=true,% auto gobble
	}
	% minted settings
	\SetupFloatingEnvironment{listing}{name=\cuz@label@codelisting}
	\SetupFloatingEnvironment{listing}{listname=\cuz@label@codelisting}

	% Change listing numbering format to match figures and tables (chapter-number)
	% In appendices (where chapter has no number), only show the listing number
	\AtBeginDocument{
		\renewcommand{\thelisting}{\ifnum\c@chapter>\z@\arabic{chapter}\cuz@label@captionnumberconnector\fi\arabic{listing}}%
	}

	% Adjust spacing for listing environment
	\captionsetup[listing]{skip=-1ex}  % Reduce space between caption and listing

	% Reduce spacing around listing environment
	% \setlength{\textfloatsep}{0pt plus 1pt minus 1pt}     % Space between float and text
	% \setlength{\floatsep}{0pt plus 1pt minus 1pt}         % Space between consecutive floats
	% \setlength{\intextsep}{0pt plus 1pt minus 1pt}        % Space for floats in the text

	% Customize line numbers
	\renewcommand{\theFancyVerbLine}{
		\textcolor[rgb]{0.5,0.8,1.0}{
			\smaller{\arabic{FancyVerbLine}}
		}
	} %

	% Inline code settings
	\setmintedinline{
		fontsize=\smaller[1],
	} %

	% Main minted settings
	\setminted{
		highlightcolor=cyan!20,% color of the high light lines
		autogobble,% automatically remove the spaces to the left of the code
		% linenos=true,% show line numbers
		fontsize=\smaller[1],% smaller font size
		escapeinside=@@,% insert latex code between @ and @
		frame=leftline,% frame styles: none or lines
		framerule=.7pt,% frame width
		numbers=left,% show line numbers on: left, right, both or none
		numbersep=-.5em,% gap between numbers and start of line
		mathescape=true,% insert math code
		obeytabs=true,% properly handle tabs instead of showing ^^I
		tabsize=4,% set tab size to 4 spaces
		% resetmargins=true,
		rulecolor=cyan,% color of frame
		style=tango,% style sheet used by pygments
		% baselinestretch=0.9,% reduce line spacing within code blocks
	} %

	% Define a custom environment for compact listings
	\usepackage{etoolbox}
	\AtBeginEnvironment{listing}{%
		% \vspace{-1.5ex}% reduce space before listing
	}
	\AtEndEnvironment{listing}{%
		\vspace{-3ex}% reduce space after listing
	}
	% Shortcuts to languages with compact spacing
	\newminted[ccode]{c}{xleftmargin=1em, xrightmargin=1em, resetmargins=true, samepage=true} %
	\newmintinline{c}{} %
	\newminted[cppcode]{cpp}{xleftmargin=1em, xrightmargin=1em, resetmargins=true, samepage=true} %
	\newmintinline{cpp}{} %
	\newminted[csharpcode]{csharp}{xleftmargin=1em, xrightmargin=1em, resetmargins=true, samepage=true} %
	\newmintinline{csharp}{} %
	\newminted[javacode]{java}{xleftmargin=1em, xrightmargin=1em, resetmargins=true, samepage=true} %
	\newmintinline{java}{} %
	\newminted[pythoncode]{python}{xleftmargin=1em, xrightmargin=1em, resetmargins=true, samepage=true} %
	\newmintinline{python}{} %
	\newminted[texcode]{tex}{xleftmargin=1em, xrightmargin=1em, resetmargins=true, samepage=true} %
	\newmintinline{tex}{} %
	\newminted[mdcode]{md}{xleftmargin=1em, xrightmargin=1em, resetmargins=true, samepage=true} %
	\newmintinline{md}{} %
\fi
%-
%-> Page background
%-
\ifartx@background%
	\definecolor{backgroundcolor}{rgb}{0.85,0.85,0.85}%
	\pagecolor{backgroundcolor}% background color
\fi
%---------------------------------------------------------------------------%
\endinput

