%---------------------------------------------------------------------------%
%->> CUZ Common Package
%---------------------------------------------------------------------------%
%-
%- This package contains common command definitions shared across all
%- CUZ document classes (cuzthesis, cuzreview, cuzopening, cuzassignment).
%-
%- Created to eliminate code duplication and improve maintainability
%- by extracting identical command definitions from multiple class files.
%-
%---------------------------------------------------------------------------%
%->> Identification
%---------------------------------------------------------------------------%
\NeedsTeXFormat{LaTeX2e}%
\ProvidesPackage{cuzcommon}[2024/01/20 v1.0 CUZ Common Definitions Package]%

%---------------------------------------------------------------------------%
%->> Common utility commands
%---------------------------------------------------------------------------%
\newcommand{\cuzifstreq}{\expandafter\ifstrequal\expandafter}% expansion control
\newcommand{\cuzifstrbk}{\expandafter\ifblank\expandafter}% expansion control

%---------------------------------------------------------------------------%
%->> Common author information commands (identical across all classes)
%---------------------------------------------------------------------------%
%-
%-> These commands have identical definitions in all document classes
%-
\newcommand{\authorid}[1]{\gdef\cuz@value@authorid{\ifcuz@blinded\cuz@label@blindedtext\else#1\fi}}
\newcommand{\advisor}[1]{\gdef\cuz@value@advisor{\ifcuz@blinded\cuz@label@blindedtext\else#1\fi}}
\newcommand{\advisortitle}[1]{\gdef\cuz@value@advisortitle{\ifcuz@blinded\cuz@label@blindedtext\else#1\fi}}
\newcommand{\coadvisor}[1]{\gdef\cuz@value@coadvisor{\ifcuz@blinded\cuz@label@blindedtext\else#1\fi}}
\newcommand{\coadvisortitle}[1]{\gdef\cuz@value@coadvisortitle{\ifcuz@blinded\cuz@label@blindedtext\else#1\fi}}
\newcommand{\degree}[1]{\gdef\cuz@value@degree{#1}}
\newcommand{\degreetype}[1]{\gdef\cuz@value@degreetype{#1}}
\newcommand{\major}[1]{\gdef\cuz@value@major{\ifcuz@blinded\cuz@label@blindedtext\else#1\fi}}
\newcommand{\class}[1]{\gdef\cuz@value@class{\ifcuz@blinded\cuz@label@blindedtext\else#1\fi}}
\newcommand{\institute}[1]{\gdef\cuz@value@institute{\ifcuz@blinded\cuz@label@blindedtext\else#1\fi}}

%---------------------------------------------------------------------------%
%->> Common date commands (identical across all classes)
%---------------------------------------------------------------------------%
\newcommand{\graduateyear}[1]{\gdef\cuz@value@graduateyear{#1}}
\newcommand{\openingdate}[1]{\gdef\cuz@value@openingdate{#1}}
\newcommand{\reviewdate}[1]{\gdef\cuz@value@reviewdate{#1}}
\newcommand{\declaredate}[1]{\gdef\cuz@value@declaredate{#1}}
\newcommand{\signature}[1]{\gdef\cuz@value@signature{#1}}

%---------------------------------------------------------------------------%
%->> Note: Formatting commands and length definitions moved to cuzthesis.cfg
%---------------------------------------------------------------------------%
%- These configuration parameters are now defined in the .cfg file for easier
%- user customization: titleformat, englishtitleformat, otherinfoformat,
%- titlelinewidth, leftinfowidth, rightinfowidth

%---------------------------------------------------------------------------%
%->> Common underline commands
%---------------------------------------------------------------------------%
%-
%-> Basic underline enhancement (identical across all classes)
%-
\ifxetex% use underline from xeCJKfntef
    % \renewcommand{\CJKunderlinecolor}{\color[rgb]{0,0,0}}% set underline color
    % \renewcommand{\uline}[1]{\CJKunderline{#1}}% unified name
\else% use underline from ulem
    \RequirePackage{ulem}%
\fi
\newcommand{\ulenhance}[2][1pt]{% enhanced underline
    \def\ULthickness{#1}% set thickness
    \uline{#2}}
\newcommand{\ulhshift}{-4em}% horizontal shift on underline
\newcommand{\ulextend}[2][350pt]{% extend underline length
    \hbox to #1{\hfill\hspace*{\ulhshift}#2\hfill}}

%---------------------------------------------------------------------------%
%->> Common page style commands
%---------------------------------------------------------------------------%
%-
%-> Redefine cleardoublepage to have page style argument (identical across all classes)
%-
\renewcommand{\cleardoublepage}[1][plain]{%
    \clearpage\if@twoside\ifodd\c@page\else%
            \thispagestyle{#1}%
            \hbox{}\newpage\if@twocolumn\hbox{}\newpage\fi\fi\fi%
}

%---------------------------------------------------------------------------%
%->> End of package
%---------------------------------------------------------------------------%
\endinput
