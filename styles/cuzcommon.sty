%---------------------------------------------------------------------------%
%-                                                                         -%
%-                             公共包                                       -%
%-                                                                         -%
%---------------------------------------------------------------------------%
%- Copyright (C) Hao XIE <<EMAIL>>
%- This is free software: you can redistribute it and/or modify it
%- under the terms of the GNU General Public License as published by
%- the Free Software Foundation, either version 3 of the License, or
%- (at your option) any later version.
%-
%- 此包包含所有CUZ文档类共享的命令定义
%- (cuzthesis, cuzreview, cuzopening, cuzassignment)
%- 创建目的是消除代码重复并提高可维护性
%-
%- 包依赖关系：
%- 1. cuzcommon.sty (本包) - 提供CUZ特定的命令定义
%- 2. artratex.sty - 提供通用的LaTeX样式功能
%- 3. cuzthesis.cfg - 提供用户可配置的参数
%-
%- 加载顺序：cuzcommon.sty → artratex.sty → cuzthesis.cfg
%---------------------------------------------------------------------------%
%->> 文件标识
%---------------------------------------------------------------------------%
\NeedsTeXFormat{LaTeX2e}%
\ProvidesPackage{cuzcommon}[2025/01/19 v1.0 CUZ Common Definitions Package]%

%---------------------------------------------------------------------------%
%->> 公共工具命令
%---------------------------------------------------------------------------%
\newcommand{\cuzifstreq}{\expandafter\ifstrequal\expandafter}% 展开控制
\newcommand{\cuzifstrbk}{\expandafter\ifblank\expandafter}% 展开控制

%---------------------------------------------------------------------------%
%->> 公共作者信息命令（所有类中定义相同）
%---------------------------------------------------------------------------%
%-
%-> 这些命令在所有文档类中具有相同的定义
%-
\newcommand{\authorid}[1]{\gdef\cuz@value@authorid{\ifcuz@blinded\cuz@label@blindedtext\else#1\fi}}
\newcommand{\advisor}[1]{\gdef\cuz@value@advisor{\ifcuz@blinded\cuz@label@blindedtext\else#1\fi}}
\newcommand{\advisortitle}[1]{\gdef\cuz@value@advisortitle{\ifcuz@blinded\cuz@label@blindedtext\else#1\fi}}
\newcommand{\coadvisor}[1]{\gdef\cuz@value@coadvisor{\ifcuz@blinded\cuz@label@blindedtext\else#1\fi}}
\newcommand{\coadvisortitle}[1]{\gdef\cuz@value@coadvisortitle{\ifcuz@blinded\cuz@label@blindedtext\else#1\fi}}
\newcommand{\degree}[1]{\gdef\cuz@value@degree{#1}}
\newcommand{\degreetype}[1]{\gdef\cuz@value@degreetype{#1}}
\newcommand{\major}[1]{\gdef\cuz@value@major{\ifcuz@blinded\cuz@label@blindedtext\else#1\fi}}
\newcommand{\class}[1]{\gdef\cuz@value@class{\ifcuz@blinded\cuz@label@blindedtext\else#1\fi}}
\newcommand{\institute}[1]{\gdef\cuz@value@institute{\ifcuz@blinded\cuz@label@blindedtext\else#1\fi}}

%---------------------------------------------------------------------------%
%->> 公共日期命令（所有类中定义相同）
%---------------------------------------------------------------------------%
\newcommand{\graduateyear}[1]{\gdef\cuz@value@graduateyear{#1}}
\newcommand{\openingdate}[1]{\gdef\cuz@value@openingdate{#1}}
\newcommand{\reviewdate}[1]{\gdef\cuz@value@reviewdate{#1}}
\newcommand{\declaredate}[1]{\gdef\cuz@value@declaredate{#1}}
\newcommand{\signature}[1]{\gdef\cuz@value@signature{#1}}

%---------------------------------------------------------------------------%
%->> 注意：格式化命令和长度定义已移动到cuzthesis.cfg
%---------------------------------------------------------------------------%
%- 这些配置参数现在在.cfg文件中定义，以便用户更容易自定义：
%- titleformat, englishtitleformat, otherinfoformat,
%- titlelinewidth, leftinfowidth, rightinfowidth

%---------------------------------------------------------------------------%
%->> 公共下划线命令
%---------------------------------------------------------------------------%
%-
%-> 基本下划线增强（所有类中定义相同）
%-
\ifxetex% 使用xeCJKfntef的下划线
    % \renewcommand{\CJKunderlinecolor}{\color[rgb]{0,0,0}}% 设置下划线颜色
    % \renewcommand{\uline}[1]{\CJKunderline{#1}}% 统一名称
\else% 使用ulem的下划线
    \RequirePackage{ulem}%
\fi
\newcommand{\ulenhance}[2][1pt]{% 增强下划线
    \def\ULthickness{#1}% 设置厚度
    \uline{#2}}
\newcommand{\ulhshift}{-4em}% 下划线水平偏移
\newcommand{\ulextend}[2][350pt]{% 扩展下划线长度
    \hbox to #1{\hfill\hspace*{\ulhshift}#2\hfill}}

%---------------------------------------------------------------------------%
%->> 公共页面样式命令
%---------------------------------------------------------------------------%
%-
%-> 重定义cleardoublepage以具有页面样式参数（所有类中定义相同）
%-
\renewcommand{\cleardoublepage}[1][plain]{%
    \clearpage\if@twoside\ifodd\c@page\else%
            \thispagestyle{#1}%
            \hbox{}\newpage\if@twocolumn\hbox{}\newpage\fi\fi\fi%
}

%---------------------------------------------------------------------------%
%->> 包结束
%---------------------------------------------------------------------------%
\endinput
