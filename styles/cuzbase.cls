% 创建一个基础类文件，包含所有共享的选项和功能
\NeedsTeXFormat{LaTeX2e}%
\ProvidesClass{cuzbase}[2024/10/18 v2.0 CUZ Base LaTeX document class]%

%---------------------------------------------------------------------------%
%->> 声明共享选项
%---------------------------------------------------------------------------%
%-
%-> 布局选项
%-
\DeclareOption{singlesided}{% 启用单面打印
    \PassOptionsToClass{oneside}{ctexbook}%
}
\newif\ifcuz@doublesided \cuz@doublesidedfalse
\DeclareOption{doublesided}{% 启用双面打印
    \PassOptionsToClass{twoside}{ctexbook}%
    \cuz@doublesidedtrue%
}
\newif\ifcuz@hdr \cuz@hdrfalse
\DeclareOption{cuzhdr}{% 启用自定义页眉页脚样式
    \cuz@hdrtrue%
}
\newif\ifcuz@printcopy \cuz@printcopyfalse
\DeclareOption{printcopy}{% 启用打印布局
    \PassOptionsToClass{twoside}{ctexbook}%
    \cuz@printcopytrue%
}
\newif\ifcuz@plain \cuz@plainfalse
\DeclareOption{plain}{
    \PassOptionsToClass{scheme=plain}{ctexbook}%
    \cuz@plaintrue%
}
\newif\ifcuz@blinded \cuz@blindedfalse
\DeclareOption{blinded}{
    \cuz@blindedtrue%
}
%-
%-> 草稿版本信息
%-
\newif\ifcuz@versioninfo \cuz@versioninfofalse
\DeclareOption{draftversion}{%
    \cuz@versioninfotrue%
}
%-
%-> 处理未实现的选项
%-
\DeclareOption*{%
    \PassOptionsToClass{\CurrentOption}{ctexbook}%
}
%-
%-> 终止所有选项处理
%-
\ProcessOptions\relax%

%---------------------------------------------------------------------------%
%->> 加载基础类信息
%---------------------------------------------------------------------------%
\LoadClass[UTF8,a4paper,zihao=-4]{ctexbook}

%---------------------------------------------------------------------------%
%->> 加载共享的必要包
%---------------------------------------------------------------------------%
\RequirePackage{iftex}% LaTeX引擎检测（替代ifxetex）
\RequirePackage{etoolbox}% 编程工具箱
\RequirePackage{textcase}% 大小写字母处理
\RequirePackage{hologo}% 一些有用的标志
\RequirePackage{framed}% 带框段落
\RequirePackage{lineno}% 行号
\RequirePackage{setspace}% 行间距
\RequirePackage{multirow}% 表格多行
% \RequirePackage{geometry}% 页面布局

% 定义通用的命令扩展控制
\newcommand{\cuzifstreq}{\expandafter\ifstrequal\expandafter}% 扩展控制
\newcommand{\cuzifstrbk}{\expandafter\ifblank\expandafter}% 扩展控制

%---------------------------------------------------------------------------%
%->> 加载类配置
%---------------------------------------------------------------------------%
\AtEndOfPackage{% 在包加载后加载配置文件，使前导命令生效
    \makeatletter
    \InputIfFileExists{styles/cuzthesis.cfg}{}{}
    \makeatother
}

%---------------------------------------------------------------------------%
%->> 页面布局
%---------------------------------------------------------------------------%
% 设置页边距和纸张大小
\RequirePackage[a4paper, margin=2.5cm]{geometry}

% 段落间距
\setlength{\parskip}{0.0\baselineskip}
% 行间距
\linespread{1.25}% 行间距设置

%---------------------------------------------------------------------------%
%->> 页眉页脚
%---------------------------------------------------------------------------%
\ifcuz@hdr% 用户自定义页眉页脚样式
    \RequirePackage{fancyhdr}
    \RequirePackage{lastpage}
    \pagestyle{fancy}%
    \providecommand{\chaptermark}{}% 兼容非书籍类
    \providecommand{\thechapter}{}% 兼容非书籍类
    \providecommand{\CTEXthechapter}{\thechapter.}% 兼容非ctex类
    %- 重置章节标记样式为实际名称
    \renewcommand{\chaptermark}[1]{\markboth{\MakeUppercase{#1}}{}}%
    %- 禁用大写效果
    \renewcommand{\MakeUppercase}[1]{#1}%
    \fancypagestyle{nostyle}{% 无页脚的页眉页脚样式
        \fancyhf{}% 清除字段
        \renewcommand{\headrulewidth}{0pt}% 页眉线
        \renewcommand{\footrulewidth}{0pt}% 页脚线
    }
    %- 重定义\nomatter以包含更改
    \providecommand{\nomatter}{}% 兼容非书籍类
    \let\cuznomatter\nomatter%
    \renewcommand{\nomatter}{%
        \cuznomatter%
        \pagestyle{nostyle}%
    }
\fi

%- 定义\plainmatter命令（无论是否启用页眉页脚都需要）
\providecommand{\plainmatter}{}% 兼容非书籍类
\let\cuzplainmatter\plainmatter%
\renewcommand{\plainmatter}{%
    \cuzplainmatter%
    \pagestyle{plain}%
}

%- 定义其他matter命令（兼容性）
\providecommand{\nofootermatter}{}% 兼容非书籍类
\providecommand{\noheadermatter}{}% 兼容非书籍类

%---------------------------------------------------------------------------%
%->> 样式控制命令
%---------------------------------------------------------------------------%
%- 重定义cleardoublepage以具有页面样式参数
\renewcommand{\cleardoublepage}[1][plain]{%
    \clearpage\if@twoside\ifodd\c@page\else%
            \thispagestyle{#1}%
            \hbox{}\newpage\if@twocolumn\hbox{}\newpage\fi\fi\fi%
}

%- 下划线
\ifxetex% 使用xeCJKfntef的下划线
    % 可以根据需要取消注释以下行
    % \renewcommand{\CJKunderlinecolor}{\color[rgb]{0,0,0}}% 设置下划线颜色
    % \renewcommand{\uline}[1]{\CJKunderline{#1}}% 统一名称
\else% 使用ulem的下划线
    \RequirePackage{ulem}%
\fi

\newcommand{\ulenhance}[2][1pt]{% 增强的下划线
    \def\ULthickness{#1}% 设置厚度
    \uline{#2}}
\newcommand{\ulhshift}{-4em}% 下划线上的水平偏移
\newcommand{\ulextend}[2][350pt]{% 扩展下划线长度
    \hbox to #1{\hfill\hspace*{\ulhshift}#2\hfill}}

%---------------------------------------------------------------------------%
%->> 共同的标题页命令定义
%---------------------------------------------------------------------------%
%-
%-> 基础信息命令
%-
\newcommand{\confidential}[1]{\gdef\cuz@value@confidential{#1}}
\newcommand{\schoollogo}[2]{
    \gdef\cuz@value@schoollogo{
        \ifcuz@blinded
            \vspace*{4.5em}
        \else
            \includegraphics[#1]{#2}
        \fi
    }
}

%-
%-> 标题相关命令
%-
\renewcommand{\title}[1]{\gdef\cuz@value@title{#1}\gdef\cuz@value@titlemark{#1}}
\newcommand{\englishtitle}[1]{\gdef\cuz@value@englishtitle{#1}}
\renewcommand{\@title}{\cuz@value@titlemark}

%-
%-> 人员信息命令
%-
\renewcommand{\author}[1]{\gdef\cuz@value@author{\ifcuz@blinded\cuz@label@blindedtext\else#1\fi}}
\newcommand{\authorid}[1]{\gdef\cuz@value@authorid{\ifcuz@blinded\cuz@label@blindedtext\else#1\fi}}
\newcommand{\advisor}[1]{\gdef\cuz@value@advisor{\ifcuz@blinded\cuz@label@blindedtext\else#1\fi}}
\newcommand{\advisortitle}[1]{\gdef\cuz@value@advisortitle{\ifcuz@blinded\cuz@label@blindedtext\else#1\fi}}
\newcommand{\coadvisor}[1]{\gdef\cuz@value@coadvisor{\ifcuz@blinded\cuz@label@blindedtext\else#1\fi}}
\newcommand{\coadvisortitle}[1]{\gdef\cuz@value@coadvisortitle{\ifcuz@blinded\cuz@label@blindedtext\else#1\fi}}

%-
%-> 学位和专业信息命令
%-
\newcommand{\degree}[1]{\gdef\cuz@value@degree{#1}}
\newcommand{\degreetype}[1]{\gdef\cuz@value@degreetype{#1}}
\newcommand{\major}[1]{\gdef\cuz@value@major{\ifcuz@blinded\cuz@label@blindedtext\else#1\fi}}
\newcommand{\class}[1]{\gdef\cuz@value@class{\ifcuz@blinded\cuz@label@blindedtext\else#1\fi}}
\newcommand{\institute}[1]{\gdef\cuz@value@institute{\ifcuz@blinded\cuz@label@blindedtext\else#1\fi}}

%-
%-> 日期相关命令
%-
\newcommand{\graduateyear}[1]{\gdef\cuz@value@graduateyear{#1}}
\newcommand{\openingdate}[1]{\gdef\cuz@value@openingdate{#1}}
\newcommand{\reviewdate}[1]{\gdef\cuz@value@reviewdate{#1}}
\newcommand{\declaredate}[1]{\gdef\cuz@value@declaredate{#1}}
\newcommand{\signature}[1]{\gdef\cuz@value@signature{#1}}

%---------------------------------------------------------------------------%
%->> 共同的格式化命令
%---------------------------------------------------------------------------%
%-
%-> 标题格式化命令
%-
\newcommand{\cuz@macro@titleformat}{\zihao{-2}\heiti\centering}
\newcommand{\cuz@macro@englishtitleformat}{\zihao{3}\rmfamily\centering}
\newcommand{\cuz@macro@otherinfoformat}{\zihao{3}\songti\centering}

%-
%-> 长度定义（各类可以根据需要重新定义）
%-
\newlength{\cuz@length@titlelinewidth}
\addtolength{\cuz@length@titlelinewidth}{300pt}
\newlength{\cuz@length@leftinfowidth}
\addtolength{\cuz@length@leftinfowidth}{115pt}
\newlength{\cuz@length@rightinfowidth}
\addtolength{\cuz@length@rightinfowidth}{145pt}

%-
%-> 下划线宏命令
%-
\newcommand{\cuz@macro@exmultiunderline}[3]{%
    \parbox[t]{#1}{\centering #3 \vspace{0.5ex} \hrule height #2}%
}

%---------------------------------------------------------------------------%
%->> 共同的包引用
%---------------------------------------------------------------------------%
\RequirePackage{titlesec}% 标题格式
\RequirePackage{lipsum}% 示例文本
\RequirePackage{tikz}% 绘图
\RequirePackage{tcolorbox}% 彩色盒子

% 导出类，使其可以被其他类使用
\endinput