%---------------------------------------------------------------------------%
%-                                                                         -%
%-                           文档类配置文件                                 -%
%-                                                                         -%
%---------------------------------------------------------------------------%
%- Copyright (C) Hao XIE <<EMAIL>>
%- This is free software: you can redistribute it and/or modify it
%- under the terms of the GNU General Public License as published by
%- the Free Software Foundation, either version 3 of the License, or
%- (at your option) any later version.
%---------------------------------------------------------------------------%
%->> 文件标识
%---------------------------------------------------------------------------%
\ProvidesFile{cuzthesis.cfg}[2018/01/07 v2.0 class configuration file]%
%---------------------------------------------------------------------------%
%->> 结构元素标签
%---------------------------------------------------------------------------%
\renewcommand*{\contentsname}{目\hskip 1em 录}
\renewcommand*{\listfigurename}{图形列表}
\renewcommand*{\listtablename}{表格列表}
\renewcommand*{\bibname}{\songti\selectfont{}参考文献}
%---------------------------------------------------------------------------%
%->> 页眉页脚标签
%---------------------------------------------------------------------------%
\def\cuz@label@university{浙江传媒学院}
\def\cuz@label@header@bacthesis{本科毕业论文}
\newcommand{\cuz@footer@author}[1]{作者:#1}
\newcommand{\cuz@footer@pagenumber}[2]{第#1页共 #2页}
%---------------------------------------------------------------------------%
%->> 中文标题页标签
%---------------------------------------------------------------------------%
\def\cuz@label@confidential{密级:}
\def\cuz@label@thesis{}
% \def\cuz@label@bacthesis{学士学位论文}
\def\cuz@label@bacthesis{毕业设计}
\def\cuz@label@masthesis{硕士学位论文}
\def\cuz@label@docthesis{博士学位论文}
\def\cuz@label@assignment{毕业设计任务书}
\def\cuz@label@opening{本科毕业设计开题报告}
\def\cuz@label@review{本科毕业设计文献综述}
\def\cuz@label@date{填表日期：}
\def\cuz@label@maker{浙江传媒学院教务处制}
\def\cuz@label@thesistitle{论文题目}
\def\cuz@label@author{学生姓名}
\def\cuz@label@authorid{学号}
\def\cuz@label@advisor{指导教师}
\def\cuz@label@advisortitle{职称}
\def\cuz@label@coadvisor{合作/企业教师}
\def\cuz@label@coadvisortitle{职称}
\def\cuz@label@degree{学位类别}
\def\cuz@label@major{专业}
\def\cuz@label@gradmajor{学科专业}
\def\cuz@label@ungradmajor{专业}
\def\cuz@label@class{班级}
\def\cuz@label@institute{}
\def\cuz@label@gradinstitute{培养单位}
\def\cuz@label@ungradinstitute{二级学院名称}
%---------------------------------------------------------------------------%
%->> 英文标题页标签
%---------------------------------------------------------------------------%
\def\cuz@label@englishstatement{A \cuz@value@englishthesistype\ submitted to the\\
	University of Chinese Academy of Sciences\\
	in partial fulfillment of the requirement\\
	for the degree of\\
    \cuz@value@englishdegree\ of \cuz@value@englishdegreetype\\
    in\ \cuz@value@englishmajor}
%---------------------------------------------------------------------------%
%->> 其他页面标签
%---------------------------------------------------------------------------%
\def\cuz@label@chineseabstract{摘要}
\def\cuz@label@chinesekeywords{关键词：}
\def\cuz@label@englishabstract{Abstract}
\def\cuz@label@englishkeywords{Keywords:}
\def\cuz@label@acknowledgement{致谢}
\def\cuz@label@appendices{附录}
\def\cuz@label@captionnumberconnector{-}
\def\cuz@label@codelisting{代码段}
\def\cuz@label@algorithmname{算法}
\def\cuz@label@algorithmautorefname{算法}
\def\cuz@label@listofalgorithmsname{算法目录}
\def\cuz@label@blindedtext{***}
%---------------------------------------------------------------------------%
%->> 声明页内容
%---------------------------------------------------------------------------%
\def\cuz@value@declare@create{郑重声明}
\def\cuz@value@declare@creativity{%
    我谨在此郑重声明：本人所写的毕业论文《\cuz@declaration@title》均系本人独立完成，没有抄袭行为，
    凡涉及其他作者的观点和材料，均作了注释，若有不实，后果由本人承担。
}
\def\cuz@value@declare@s{承诺人（签名）：}
\def\cuz@value@declare@d{\qquad\qquad 年\qquad 月\qquad 日}
\def\cuz@value@englishbibetal{et al.}
\def\cuz@value@englishbiband{ and }
\def\cuz@value@chinesebibetal{等}
\def\cuz@value@chinesebiband{和}
%---------------------------------------------------------------------------%
%->> 格式化命令配置
%---------------------------------------------------------------------------%
%- 标题页格式化命令（从cuzcommon.sty移动到此处以便用户自定义）
%- 注意：cuzassignment.cls有自己的\cuz@macro@otherinfoformat定义
\newcommand{\cuz@macro@titleformat}{\zihao{-2}\heiti\centering}
\newcommand{\cuz@macro@englishtitleformat}{\zihao{3}\rmfamily\centering}
\providecommand{\cuz@macro@otherinfoformat}{\zihao{3}\songti\centering}

%---------------------------------------------------------------------------%
%->> 长度定义配置
%---------------------------------------------------------------------------%
%- 页面布局长度定义（从cuzcommon.sty移动到此处以便用户自定义）
%- 注意：cuzassignment.cls有自己的长度定义，使用不同的数值
\@ifundefined{cuz@length@titlelinewidth}{%
    \newlength{\cuz@length@titlelinewidth}
    \addtolength{\cuz@length@titlelinewidth}{300pt}
}{}
\@ifundefined{cuz@length@leftinfowidth}{%
    \newlength{\cuz@length@leftinfowidth}
    \addtolength{\cuz@length@leftinfowidth}{115pt}
}{}
\@ifundefined{cuz@length@rightinfowidth}{%
    \newlength{\cuz@length@rightinfowidth}
    \addtolength{\cuz@length@rightinfowidth}{145pt}
}{}

%---------------------------------------------------------------------------%
%->> 结构元素配置
%---------------------------------------------------------------------------%
%- 章节格式
\ctexset {
    chapter = {
        format = \linespread{1.5}\zihao{4}\bfseries\raggedright,%\sffamily\centering,
        number = \arabic{chapter},
        name = {},
        aftername = \hskip 0.5em,
        beforeskip = 1.5ex,
        afterskip = 1.5ex,
        % pagestyle = plain,
        % break = \par,
    }
}
%- 节格式
\ctexset {
    section = {
        format = \linespread{1.5}\zihao{-4}\bfseries\raggedright,
        aftername = \hskip 0.5em,
        beforeskip = 0.4ex,
        afterskip = 0.4ex,
        indent = 2em,
    }
}
%- 小节格式
\ctexset {
    subsection = {
        format = \linespread{1.5}\zihao{-4}\bfseries\raggedright,
        aftername = \hskip 0.5em,
        beforeskip = 0.4ex,
        afterskip = 0.4ex,
        indent = 2em,
    }
}
%- 子小节格式
\ctexset {
    subsubsection = {
        format = \linespread{1.5}\zihao{-4}\bfseries\raggedright,
        aftername = \hskip 0.5em,
        beforeskip = 0.4ex,
        afterskip = 0.4ex,
        indent = 2em,
    }
}
%- 附录格式
\ctexset {
    appendix = {
    }
}
%---------------------------------------------------------------------------%
\endinput

